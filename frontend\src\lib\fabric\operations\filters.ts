import { Canvas, FabricImage, filters } from "fabric";
import { FilterParams, PartialFilterParams } from "@/shared/types";
import { defaultFabricConfigs } from "@/config/defaultFabricConfigs";

const canvasFilterStates = new Map<Canvas, FilterParams>();

export const applyCanvasFilters = (canvas: Canvas, partialFilters: PartialFilterParams): void => {
  let currentState = canvasFilterStates.get(canvas);
  if (!currentState) {
    currentState = { ...defaultFabricConfigs };
    canvasFilterStates.set(canvas, currentState);
  }
  const newState = { ...currentState, ...partialFilters };
  canvasFilterStates.set(canvas, newState);
  const filterArray: any[] = [];
  if (newState.brightness !== 1) {
    filterArray.push(new filters.Brightness({ brightness: newState.brightness - 1 }));
  }
  if (newState.contrast !== 1) {
    filterArray.push(new filters.Contrast({ contrast: newState.contrast - 1 }));
  }
  if (newState.grayscale) {
    filterArray.push(new filters.Grayscale());
  }
  if (newState.invert) {
    filterArray.push(new filters.Invert());
  }
  if (newState.sharpness !== 1) {
    const matrix =
      newState.sharpness > 1
        ? [0, -1, 0, -1, 5, -1, 0, -1, 0]
        : [1 / 9, 1 / 9, 1 / 9, 1 / 9, 1 / 9, 1 / 9, 1 / 9, 1 / 9, 1 / 9];
    filterArray.push(new filters.Convolute({ matrix }));
  }
  if (newState.gammaR !== 1 || newState.gammaG !== 1 || newState.gammaB !== 1) {
    filterArray.push(
      new filters.Gamma({
        gamma: [newState.gammaR, newState.gammaG, newState.gammaB],
      })
    );
  }
  canvas.forEachObject((obj) => {
    if (obj instanceof FabricImage) {
      (obj as any).filters = [...filterArray];
      obj.applyFilters();
    }
  });
  if (canvas.backgroundImage instanceof FabricImage) {
    (canvas.backgroundImage as any).filters = [...filterArray];
    canvas.backgroundImage.applyFilters();
  }
  canvas.renderAll();
};
