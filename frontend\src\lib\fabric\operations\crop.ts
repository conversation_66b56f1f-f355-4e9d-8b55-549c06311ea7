import { Canvas, Rect } from "fabric";
import { CropData } from "@/shared/types";
import { loadCanvasImage } from "../rendering/image";

// Hide annotations temporarily during crop operation
export const hideAnnotationsForCrop = (canvas: Canvas) => {
  const annotations = canvas.getObjects().filter((obj) => {
    const objName = (obj as any).name;
    return objName !== "backgroundImage" && objName !== "cropRect";
  });

  // Log annotation properties before hiding
  console.log("=== BEFORE CROP - Annotation Properties ===");
  annotations.forEach((obj, index) => {
    console.log(`Annotation ${index}:`, {
      name: (obj as any).name,
      type: obj.type,
      left: obj.left,
      top: obj.top,
      scaleX: obj.scaleX,
      scaleY: obj.scaleY,
      strokeWidth: (obj as any).strokeWidth,
      visible: obj.visible,
    });
  });

  const previousVisibilities = annotations.map((obj) => obj.visible);
  annotations.forEach((obj) => obj.set({ visible: false }));

  return { annotations, previousVisibilities };
};

// Restore annotations visibility and adjust coordinates for crop
export const restoreAndAdjustAnnotations = (
  canvas: Canvas,
  annotations: any[],
  previousVisibilities: boolean[],
  cropLeft: number,
  cropTop: number,
  cropScale: number
) => {
  console.log("=== CROP TRANSFORMATION PARAMETERS ===");
  console.log("Crop Left:", cropLeft);
  console.log("Crop Top:", cropTop);
  console.log("Crop Scale:", cropScale);

  // Restore visibility first
  annotations.forEach((obj, i) => obj.set({ visible: previousVisibilities[i] }));

  // Apply crop transformation to annotations similar to how rotation works
  // We need to transform the objects to match the cropped view
  annotations.forEach((obj, index) => {
    // Store original coordinates
    const originalLeft = obj.left || 0;
    const originalTop = obj.top || 0;
    const originalScaleX = obj.scaleX || 1;
    const originalScaleY = obj.scaleY || 1;
    const originalStrokeWidth = (obj as any).strokeWidth;

    console.log(`=== BEFORE TRANSFORMATION - Annotation ${index} ===`);
    console.log("Original:", {
      left: originalLeft,
      top: originalTop,
      scaleX: originalScaleX,
      scaleY: originalScaleY,
      strokeWidth: originalStrokeWidth,
    });

    // Transform coordinates: first translate by crop offset, then scale
    const newLeft = (originalLeft - cropLeft) * cropScale;
    const newTop = (originalTop - cropTop) * cropScale;
    const newScaleX = originalScaleX * cropScale;
    const newScaleY = originalScaleY * cropScale;

    // Handle stroke width scaling
    let newStrokeWidth = originalStrokeWidth;
    if (originalStrokeWidth && typeof originalStrokeWidth === "number") {
      // Scale the stroke width proportionally
      newStrokeWidth = originalStrokeWidth * cropScale;
    }

    console.log("Calculated new values:", {
      newLeft,
      newTop,
      newScaleX,
      newScaleY,
      newStrokeWidth,
    });

    obj.set({
      left: newLeft,
      top: newTop,
      scaleX: newScaleX,
      scaleY: newScaleY,
      strokeWidth: newStrokeWidth,
    });

    // Set strokeUniform to false so stroke scales with the object
    if ("strokeUniform" in obj) {
      obj.strokeUniform = false;
    }

    // Handle text objects to maintain readability
    if (obj.type === "textbox" || obj.type === "text") {
      const textbox = obj as any;
      // Reset text scaling and adjust font size instead
      textbox.set({
        fontSize: 16,
        scaleX: 1,
        scaleY: 1,
        lockScalingX: true,
        lockScalingY: true,
        hasControls: false,
      });
    }

    obj.setCoords();

    console.log(`=== AFTER TRANSFORMATION - Annotation ${index} ===`);
    console.log("Final:", {
      left: obj.left,
      top: obj.top,
      scaleX: obj.scaleX,
      scaleY: obj.scaleY,
      strokeWidth: (obj as any).strokeWidth,
    });
  });
};

// Create a DOMRect-like object from crop dimensions
export const createImageLoadContainer = (
  cropData: CropData,
  fallbackRect?: DOMRect
): DOMRect | undefined => {
  if (!cropData.canvasDimensions) return fallbackRect;

  return {
    width: cropData.canvasDimensions.width,
    height: cropData.canvasDimensions.height,
    x: 0,
    y: 0,
    top: 0,
    left: 0,
    bottom: cropData.canvasDimensions.height,
    right: cropData.canvasDimensions.width,
    toJSON: () => ({}),
  } as DOMRect;
};

// Calculate scaled image dimensions
export const getScaledImageDimensions = (canvas: Canvas) => {
  const backgroundImage = canvas.backgroundImage;
  if (!backgroundImage) {
    return {
      originalWidth: 512,
      originalHeight: 512,
      scale: 1,
      scaledWidth: 512,
      scaledHeight: 512,
    };
  }

  const originalWidth = backgroundImage.width || 512;
  const originalHeight = backgroundImage.height || 512;
  const scale = backgroundImage.scaleX || 1;
  const scaledWidth = originalWidth * scale;
  const scaledHeight = originalHeight * scale;

  return {
    originalWidth,
    originalHeight,
    scale,
    scaledWidth,
    scaledHeight,
  };
};

// Calculate crop rectangle coordinates in canvas space
export const calculateCropCoordinates = (
  cropData: CropData,
  scaledWidth: number,
  scaledHeight: number
) => {
  if (!cropData.normalizedCropRect) {
    throw new Error("Normalized crop rect is required");
  }

  return {
    left: cropData.normalizedCropRect.left * scaledWidth,
    top: cropData.normalizedCropRect.top * scaledHeight,
    width: cropData.normalizedCropRect.width * scaledWidth,
    height: cropData.normalizedCropRect.height * scaledHeight,
  };
};

// Create a clip rectangle for cropping
export const createClipRect = (left: number, top: number, width: number, height: number): Rect => {
  return new Rect({
    left,
    top,
    width,
    height,
    absolutePositioned: true,
    selectable: false,
    evented: false,
  });
};

// Calculate viewport transform for crop display
export const calculateCropViewportTransform = (
  canvasWidth: number,
  canvasHeight: number,
  cropLeft: number,
  cropTop: number,
  cropWidth: number,
  cropHeight: number
): [number, number, number, number, number, number] => {
  // Use uniform scaling to maintain aspect ratio
  const scale = Math.min(canvasWidth / cropWidth, canvasHeight / cropHeight);

  return [scale, 0, 0, scale, -cropLeft * scale, -cropTop * scale];
};

// Apply crop to canvas with all necessary transformations
export const applyCropToCanvas = (
  canvas: Canvas,
  cropData: CropData,
  containerRect?: DOMRect
): void => {
  if (!cropData.normalizedCropRect) return;

  const { scaledWidth, scaledHeight } = getScaledImageDimensions(canvas);
  const { left, top, width, height } = calculateCropCoordinates(
    cropData,
    scaledWidth,
    scaledHeight
  );

  // Use the same approach as the working crop operation
  const viewportContainer = cropData.canvasDimensions || containerRect;
  if (viewportContainer) {
    // Use uniform scaling to maintain aspect ratio
    const scale = Math.min(viewportContainer.width / width, viewportContainer.height / height);

    // Calculate actual canvas dimensions based on scaled crop area
    const actualWidth = width * scale;
    const actualHeight = height * scale;

    // Transform all objects (including annotations) to match the crop
    canvas.forEachObject((obj) => {
      if ((obj as any).name === "backgroundImage") {
        // Transform background image
        obj.set({
          left: -left * scale,
          top: -top * scale,
          scaleX: (obj.scaleX || 1) * scale,
          scaleY: (obj.scaleY || 1) * scale,
        });
      } else {
        // Transform annotations
        const originalLeft = obj.left || 0;
        const originalTop = obj.top || 0;
        const originalScaleX = obj.scaleX || 1;
        const originalScaleY = obj.scaleY || 1;
        const originalStrokeWidth = (obj as any).strokeWidth;

        // Handle stroke width scaling
        let newStrokeWidth = originalStrokeWidth;
        if (originalStrokeWidth && typeof originalStrokeWidth === "number") {
          newStrokeWidth = originalStrokeWidth * scale;
        }

        obj.set({
          left: (originalLeft - left) * scale,
          top: (originalTop - top) * scale,
          scaleX: originalScaleX * scale,
          scaleY: originalScaleY * scale,
          strokeWidth: newStrokeWidth,
        });

        if ("strokeUniform" in obj) {
          obj.strokeUniform = false;
        }

        if (obj.type === "textbox" || obj.type === "text") {
          const textbox = obj as any;
          textbox.set({
            fontSize: 16,
            scaleX: 1,
            scaleY: 1,
            lockScalingX: true,
            lockScalingY: true,
            hasControls: false,
          });
        }

        obj.setCoords();
      }
    });

    // Set canvas dimensions to exactly match the scaled crop area
    canvas.setDimensions({ width: actualWidth, height: actualHeight });
  }

  canvas.renderAll();
};

// Handle crop operation
export const handleCropOperation = (
  fabricCanvas: React.RefObject<Canvas | null>,
  hasPerformedCrop: boolean,
  setCropData: (data: CropData) => void,
  isUndoing: React.MutableRefObject<boolean>,
  setHasPerformedCrop: (value: boolean) => void,
  containerRef?: React.RefObject<HTMLElement | null>,
  originalImageUrl?: React.MutableRefObject<string>
) => {
  return async () => {
    if (!fabricCanvas?.current) return;
    const canvas = fabricCanvas.current;

    if (hasPerformedCrop) {
      return await restoreCroppedCanvas(
        canvas,
        setCropData,
        setHasPerformedCrop,
        containerRef,
        originalImageUrl
      );
    }

    let cropRect = canvas.getActiveObject();
    if (!(cropRect instanceof Rect) || (cropRect as any).name !== "cropRect") {
      cropRect = canvas.getObjects().find((obj) => (obj as any).name === "cropRect");
    }

    if (!cropRect || !canvas.backgroundImage) return;

    isUndoing.current = true;

    // Get the crop rectangle coordinates - use the actual rectangle dimensions
    const left = cropRect.left || 0;
    const top = cropRect.top || 0;
    const width = (cropRect.width || 0) * (cropRect.scaleX || 1);
    const height = (cropRect.height || 0) * (cropRect.scaleY || 1);

    console.log("=== CROP RECTANGLE COORDINATES ===");
    console.log("Crop Rectangle:", { left, top, width, height });

    // Hide annotations temporarily during crop
    const { annotations, previousVisibilities } = hideAnnotationsForCrop(canvas);

    // Remove only the crop rectangle
    cropRect.set({ visible: false });
    canvas.remove(cropRect);
    canvas.discardActiveObject();

    if (containerRef?.current) {
      const containerBounds = containerRef.current.getBoundingClientRect();

      console.log("=== CONTAINER AND SCALING ===");
      console.log("Container bounds:", {
        width: containerBounds.width,
        height: containerBounds.height,
      });
      console.log("Crop dimensions:", { width, height });

      // Use uniform scaling to maintain aspect ratio
      const scale = Math.min(containerBounds.width / width, containerBounds.height / height);

      console.log("Calculated scale:", scale);

      // Calculate actual canvas dimensions based on scaled crop area
      const actualWidth = width * scale;
      const actualHeight = height * scale;

      console.log("New canvas dimensions:", { actualWidth, actualHeight });

      // Transform annotations to match the crop
      restoreAndAdjustAnnotations(canvas, annotations, previousVisibilities, left, top, scale);

      // Transform the background image
      if (canvas.backgroundImage) {
        const bgImg = canvas.backgroundImage;
        bgImg.set({
          left: -left * scale,
          top: -top * scale,
          scaleX: (bgImg.scaleX || 1) * scale,
          scaleY: (bgImg.scaleY || 1) * scale,
        });
      }

      // Set canvas dimensions to exactly match the scaled crop area
      canvas.setDimensions({ width: actualWidth, height: actualHeight });

      canvas.renderAll();
    }

    // Get the actual rendered dimensions of the background image
    const backgroundImage = canvas.backgroundImage;
    if (!backgroundImage) return;

    const imageWidth = backgroundImage.width || 512;
    const imageHeight = backgroundImage.height || 512;
    const imageScaleX = backgroundImage.scaleX || 1;
    const imageScaleY = backgroundImage.scaleY || 1;

    // Calculate the actual rendered size of the background image
    const renderedImageWidth = imageWidth * imageScaleX;
    const renderedImageHeight = imageHeight * imageScaleY;

    // Get background image position (typically at origin for our use case)
    const backgroundImageLeft = backgroundImage.left || 0;
    const backgroundImageTop = backgroundImage.top || 0;

    // Calculate crop coordinates relative to the background image
    const relativeLeft = left - backgroundImageLeft;
    const relativeTop = top - backgroundImageTop;

    const normalizedCropRect = {
      left: relativeLeft / renderedImageWidth,
      top: relativeTop / renderedImageHeight,
      width: width / renderedImageWidth,
      height: height / renderedImageHeight,
    };

    const cropResult: CropData = {
      isCropped: true,
      normalizedCropRect,
      canvasDimensions: containerRef?.current
        ? {
            width: containerRef.current.getBoundingClientRect().width,
            height: containerRef.current.getBoundingClientRect().height,
          }
        : undefined,
    };
    setCropData(cropResult);

    canvas.renderAll();
    setHasPerformedCrop(true);
    isUndoing.current = false;
  };
};

// Restore canvas from cropped state
export const restoreCroppedCanvas = async (
  canvas: Canvas,
  setCropData: (data: CropData) => void,
  setHasPerformedCrop: (value: boolean) => void,
  containerRef?: React.RefObject<HTMLElement | null>,
  originalImageUrl?: React.MutableRefObject<string>
) => {
  if (!canvas) return;

  // Clear crop-related settings
  canvas.clipPath = undefined;
  canvas.setViewportTransform([1, 0, 0, 1, 0, 0]);

  // Set canvas dimensions
  if (containerRef?.current) {
    const containerBounds = containerRef.current.getBoundingClientRect();
    canvas.setDimensions({ width: containerBounds.width, height: containerBounds.height });
  }

  // Reload the original image (this will restore annotations from saved state)
  if (originalImageUrl?.current) {
    console.log("=== RESTORING ORIGINAL IMAGE AFTER CROP UNDO ===");
    const containerRect = containerRef?.current?.getBoundingClientRect();
    await loadCanvasImage(canvas, originalImageUrl.current, {
      containerRect: containerRect || undefined,
    });

    // Log annotations after restoration
    console.log("=== AFTER CROP UNDO - Annotation Properties ===");
    const annotations = canvas.getObjects().filter((obj) => {
      const objName = (obj as any).name;
      return objName !== "backgroundImage" && objName !== "cropRect";
    });
    annotations.forEach((obj, index) => {
      console.log(`Annotation ${index}:`, {
        name: (obj as any).name,
        type: obj.type,
        left: obj.left,
        top: obj.top,
        scaleX: obj.scaleX,
        scaleY: obj.scaleY,
        strokeWidth: (obj as any).strokeWidth,
        visible: obj.visible,
      });
    });
  }

  // Update state
  setCropData({
    isCropped: false,
    normalizedCropRect: undefined,
    canvasDimensions: undefined,
  });

  setHasPerformedCrop(false);

  // Render the canvas
  canvas.renderAll();
};
