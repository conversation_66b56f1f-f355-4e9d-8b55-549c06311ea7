import {
  <PERSON>a<PERSON><PERSON><PERSON>oint<PERSON>,
  FaPen,
  FaFont,
  FaSquare,
  FaMinus,
  FaCircle,
  FaCrop,
  FaRuler,
} from "react-icons/fa";
import { ToolDefinition } from "@/shared/types";

export const toolDefinitions: ToolDefinition[] = [
  {
    icon: FaMousePointer,
    title: "Select/Move objects",
    mode: "select",
  },
  {
    icon: FaPen,
    title: "Draw freehand paths",
    mode: "freehand",
  },
  {
    icon: FaFont,
    title: "Add text",
    mode: "text",
  },
  {
    icon: FaSquare,
    title: "Add rectangle",
    mode: "rect",
  },
  {
    icon: FaMinus,
    title: "Add line",
    mode: "line",
  },
  {
    icon: FaCircle,
    title: "Add circle",
    mode: "circle",
  },
  {
    icon: FaCrop,
    title: "Crop image",
    mode: "crop",
  },
  {
    icon: FaRuler,
    title: "Measure distance",
    mode: "measure",
  },
];
