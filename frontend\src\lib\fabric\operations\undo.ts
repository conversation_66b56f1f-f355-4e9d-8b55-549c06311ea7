import { Canvas } from "fabric";
import { UndoAction, FabricMeasurementLine } from "@/shared/types";

export const createUndoHandler = (
  fabricCanvas: React.RefObject<Canvas | null>,
  undoStack: UndoAction[],
  setUndoStack: React.Dispatch<React.SetStateAction<UndoAction[]>>,
  initialObjectCount: React.MutableRefObject<number>,
  isUndoing: React.MutableRefObject<boolean>
) => {
  return async () => {
    if (!fabricCanvas?.current || !undoStack.length) return;

    const canvas = fabricCanvas.current;
    const action = undoStack[undoStack.length - 1];

    console.log("=== UNDO OPERATION ===");
    console.log("Action type:", action.type);

    if (action.type === "crop") {
      setUndoStack((prev) => prev.slice(0, -1));
      return;
    }

    isUndoing.current = true;

    if (action.type === "add") {
      const objects = canvas.getObjects();
      if (objects.length > initialObjectCount.current) {
        const removedObj = objects[objects.length - 1];
        console.log("Removing object:", {
          type: removedObj.type,
          left: removedObj.left,
          top: removedObj.top,
          strokeWidth: (removedObj as any).strokeWidth,
        });
        canvas.remove(removedObj);
        canvas.renderAll();
      }
    } else if (action.type === "add-measurement") {
      const lineObj = canvas
        .getObjects()
        .find(
          (obj) => (obj as unknown as { id: string }).id === action.lineId
        ) as FabricMeasurementLine;
      if (lineObj) {
        console.log("Removing measurement line:", action.lineId);
        canvas.remove(lineObj);
        if (lineObj.measurementText) {
          canvas.remove(lineObj.measurementText);
        }
      }
      canvas.renderAll();
    } else if (action.type === "modify") {
      const obj = canvas
        .getObjects()
        .find((o) => (o as unknown as { id: string }).id === action.objectId);
      if (obj && action.previousState) {
        console.log("Restoring object state:", {
          objectId: action.objectId,
          previousState: action.previousState,
          currentState: {
            left: obj.left,
            top: obj.top,
            scaleX: obj.scaleX,
            scaleY: obj.scaleY,
          },
        });
        obj.set(action.previousState);
        obj.setCoords();
        canvas.renderAll();
      }
    }

    // Log final state after undo
    console.log("=== AFTER UNDO - Final Canvas State ===");
    const annotations = canvas.getObjects().filter((obj) => {
      const objName = (obj as any).name;
      return objName !== "backgroundImage" && objName !== "cropRect";
    });
    annotations.forEach((obj, index) => {
      console.log(`Annotation ${index}:`, {
        name: (obj as any).name,
        type: obj.type,
        left: obj.left,
        top: obj.top,
        scaleX: obj.scaleX,
        scaleY: obj.scaleY,
        strokeWidth: (obj as any).strokeWidth,
        visible: obj.visible,
      });
    });

    setUndoStack((prev) => prev.slice(0, -1));
    isUndoing.current = false;
  };
};
