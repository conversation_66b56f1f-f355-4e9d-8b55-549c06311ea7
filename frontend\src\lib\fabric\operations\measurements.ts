import { Canvas, FabricText } from "fabric";
import { FabricMeasurementLine } from "@/shared/types";

export const formatDistance = (distance: number): string => {
  if (typeof distance !== "number" || isNaN(distance) || !isFinite(distance)) {
    return "0.00";
  }
  return `${(distance * 0.2645833333).toFixed(2)} mm`;
};

export const getImageScaleFactor = (canvas: Canvas) => {
  const bg = canvas.backgroundImage;
  if (!bg) return { x: 1, y: 1 };

  const intrinsicWidth = bg.width || 1;
  const intrinsicHeight = bg.height || 1;

  const renderedWidth = intrinsicWidth * (bg.scaleX || 1);
  const renderedHeight = intrinsicHeight * (bg.scaleY || 1);

  return {
    x: renderedWidth / intrinsicWidth,
    y: renderedHeight / intrinsicHeight,
  };
};

export const createMeasurementText = (line: FabricMeasurementLine, canvas: Canvas): FabricText => {
  const lineCenter = line.getCenterPoint();
  // Absolute endpoints on canvas
  const x1 = line.left! + line.x1! * line.scaleX!;
  const y1 = line.top! + line.y1! * line.scaleY!;
  const x2 = line.left! + line.x2! * line.scaleX!;
  const y2 = line.top! + line.y2! * line.scaleY!;

  const dx = x2 - x1;
  const dy = y2 - y1;
  const scale = getImageScaleFactor(canvas);
  const realDx = dx / scale.x;
  const realDy = dy / scale.y;
  const realDistance = Math.sqrt(realDx * realDx + realDy * realDy);

  // Convert canvas px to mm (assuming 96 DPI)
  const pxToMM = 0.2645833333;
  const distanceMM = realDistance * pxToMM;

  const text = new FabricText(`${distanceMM.toFixed(2)} mm`, {
    fontSize: 16,
    left: lineCenter.x,
    top: lineCenter.y - 20,
    fill: "#ff0000",
    backgroundColor: "rgba(255, 255, 255, 0.8)",
    selectable: false,
    evented: false,
    name: "measurementText",
    originX: "center",
    originY: "center",
  });

  return text;
};

export const updateMeasurementText = (canvas: Canvas, line: FabricMeasurementLine): void => {
  if (!line.measurementText) {
    line.measurementText = createMeasurementText(line, canvas);
    canvas.add(line.measurementText);
  }

  const lineCenter = line.getCenterPoint();
  // Absolute endpoints on canvas
  const x1 = line.left! + line.x1! * line.scaleX!;
  const y1 = line.top! + line.y1! * line.scaleY!;
  const x2 = line.left! + line.x2! * line.scaleX!;
  const y2 = line.top! + line.y2! * line.scaleY!;

  const dx = x2 - x1;
  const dy = y2 - y1;
  const scale = getImageScaleFactor(canvas);
  const realDx = dx / scale.x;
  const realDy = dy / scale.y;
  const realDistance = Math.sqrt(realDx * realDx + realDy * realDy);

  // Convert canvas px to mm (assuming 96 DPI)
  const pxToMM = 0.2645833333;
  const distanceMM = realDistance * pxToMM;

  line.measurementText.set({
    text: `${distanceMM.toFixed(2)} mm`,
    left: lineCenter.x,
    top: lineCenter.y - 20,
  });

  canvas.renderAll();
};

export const updateMeasurementOnModify = (canvas: Canvas, line: FabricMeasurementLine): void => {
  if (!line.measurementText) return;

  const lineCenter = line.getCenterPoint();
  line.measurementText.set({
    left: lineCenter.x,
    top: lineCenter.y - 20,
  });

  canvas.renderAll();
};

export const cleanupOrphanedMeasurementTexts = (canvas: Canvas): void => {
  const allObjects = canvas.getObjects();
  const measurementLines = allObjects.filter((obj: any) => obj.name === "measurementLine");
  const measurementTexts = allObjects.filter((obj: any) => obj.name === "measurementText");

  const linkedTexts = new Set();
  measurementLines.forEach((line: any) => {
    if (line.measurementText) {
      linkedTexts.add(line.measurementText);
    }
  });

  const orphanedTexts = measurementTexts.filter((text) => !linkedTexts.has(text));
  orphanedTexts.forEach((text) => {
    canvas.remove(text);
  });

  canvas.renderAll();
};

export const isMeasurementLine = (obj: unknown): obj is FabricMeasurementLine => {
  return (obj as any)?.name === "measurementLine";
};
