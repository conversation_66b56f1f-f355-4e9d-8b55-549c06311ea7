import { useRef, useState } from "react";
import { Canvas } from "fabric";
import { UndoAction, UndoTrackingState } from "@/shared/types";
import { createUndoHandler } from "@/lib/fabric/operations";

export const useUndoTracking = (
  fabricCanvas: React.RefObject<Canvas | null>,
  initialObjectCount: React.MutableRefObject<number>
): UndoTrackingState => {
  const [undoStack, setUndoStack] = useState<UndoAction[]>([]);
  const isUndoingRef = useRef<boolean>(false);

  // Create undo handler
  const handleUndo = createUndoHandler(
    fabricCanvas,
    undoStack,
    setUndoStack,
    initialObjectCount,
    isUndoingRef
  );

  // Add action to undo stack
  const addUndoAction = (action: UndoAction) => {
    if (isUndoingRef.current) return;
    setUndoStack((prev) => [...prev, action]);
  };

  // Disable undo tracking (used during operations that shouldn't be tracked)
  const disableUndoTracking = () => {
    isUndoingRef.current = true;
  };

  // Enable undo tracking
  const enableUndoTracking = () => {
    isUndoingRef.current = false;
  };

  return {
    undoStack,
    canUndo: undoStack.length > 0,
    isUndoingRef,
    handleUndo,
    disableUndoTracking,
    enableUndoTracking,
    addUndoAction,
  };
};
