import { Canvas } from "fabric";
import {
  applyCanvasFilters,
  loadAnnotations,
  applyCanvasRotation,
  applyCanvasFlipHorizontal,
  applyCanvasFlipVertical,
} from "@/lib/fabric/operations";
import { loadCanvasImage } from "@/lib/fabric/rendering";
import { defaultFabricConfigs } from "@/config/defaultFabricConfigs";
import { CropData, TransformState } from "@/shared/types";

export const createShowOriginalHandler = (
  fabricCanvas: React.RefObject<Canvas | null>,
  isShowingOriginal: boolean,
  setIsShowingOriginal: (value: boolean) => void,
  isUndoing: React.MutableRefObject<boolean>,
  cachedState: React.MutableRefObject<{
    annotations: any;
    filters: Record<string, any>;
    cropData: CropData;
    transformState: TransformState;
  } | null>,
  filters: Record<string, any>,
  canvasCropData: CropData,
  transformState: TransformState,
  originalImageUrl: React.MutableRefObject<string>,
  setCropData: (data: CropData) => void,
  setTransformState: React.Dispatch<React.SetStateAction<TransformState>>,
  setHasPerformedCrop: (value: boolean) => void,
  containerRef?: React.RefObject<HTMLElement | null>
) => {
  return async () => {
    if (!fabricCanvas?.current) return;
    const canvas = fabricCanvas.current;

    isUndoing.current = true;

    if (!isShowingOriginal) {
      cachedState.current = {
        annotations: canvas.toObject(["name", "id"]),
        filters: filters,
        cropData: canvasCropData,
        transformState,
      };

      Array.from({ length: (4 - transformState.rotations) % 4 }, () => applyCanvasRotation(canvas));
      if (transformState.flipHorizontal) applyCanvasFlipHorizontal(canvas);
      if (transformState.flipVertical) applyCanvasFlipVertical(canvas);
      setTransformState({ rotations: 0, flipHorizontal: false, flipVertical: false });

      canvas.forEachObject((obj) => {
        if ((obj as any).name !== "backgroundImage") canvas.remove(obj);
      });

      canvas.clipPath = undefined;
      canvas.setViewportTransform([1, 0, 0, 1, 0, 0]);

      if (containerRef?.current) {
        const containerBounds = containerRef.current.getBoundingClientRect();
        canvas.setDimensions({ width: containerBounds.width, height: containerBounds.height });
      }

      applyCanvasFilters(canvas, defaultFabricConfigs);
      const containerRect = containerRef?.current?.getBoundingClientRect();
      await loadCanvasImage(canvas, originalImageUrl.current, {
        containerRect: containerRect || undefined,
      });
      setIsShowingOriginal(true);
    } else {
      if (cachedState.current) {
        const containerRect = containerRef?.current?.getBoundingClientRect();
        await loadCanvasImage(canvas, originalImageUrl.current, {
          containerRect: containerRect || undefined,
        });

        applyCanvasFilters(canvas, cachedState.current.filters);

        for (let i = 0; i < cachedState.current.transformState.rotations; i++) {
          applyCanvasRotation(canvas);
        }
        if (cachedState.current.transformState.flipHorizontal) applyCanvasFlipHorizontal(canvas);
        if (cachedState.current.transformState.flipVertical) applyCanvasFlipVertical(canvas);

        // Load annotations first
        await loadAnnotations(canvas, cachedState.current.annotations);

        if (
          cachedState.current.cropData?.isCropped &&
          cachedState.current.cropData.normalizedCropRect
        ) {
          console.log("=== SHOW ORIGINAL - APPLYING CROP STATE ===");
          const { normalizedCropRect } = cachedState.current.cropData;

          if (canvas.backgroundImage) {
            const backgroundImage = canvas.backgroundImage;
            const scaledImageWidth = (backgroundImage.width || 512) * (backgroundImage.scaleX || 1);
            const scaledImageHeight =
              (backgroundImage.height || 512) * (backgroundImage.scaleY || 1);

            const left = normalizedCropRect.left * scaledImageWidth;
            const top = normalizedCropRect.top * scaledImageHeight;
            const width = normalizedCropRect.width * scaledImageWidth;
            const height = normalizedCropRect.height * scaledImageHeight;

            if (containerRef?.current) {
              const containerRect = containerRef.current.getBoundingClientRect();

              const scale = Math.min(containerRect.width / width, containerRect.height / height);
              const actualWidth = width * scale;
              const actualHeight = height * scale;

              console.log("Show original crop parameters:", {
                left,
                top,
                width,
                height,
                scale,
                actualWidth,
                actualHeight,
              });

              // Transform all objects (including annotations) to match the crop - same as crop operation
              canvas.forEachObject((obj) => {
                if ((obj as any).name === "backgroundImage") {
                  // Transform background image
                  obj.set({
                    left: -left * scale,
                    top: -top * scale,
                    scaleX: (obj.scaleX || 1) * scale,
                    scaleY: (obj.scaleY || 1) * scale,
                  });
                } else {
                  // Transform annotations - same logic as crop operation
                  const originalLeft = obj.left || 0;
                  const originalTop = obj.top || 0;
                  const originalScaleX = obj.scaleX || 1;
                  const originalScaleY = obj.scaleY || 1;
                  const originalStrokeWidth = (obj as any).strokeWidth;

                  // Calculate adjusted stroke width to compensate for scaling
                  const newScaleX = originalScaleX * scale;
                  const newScaleY = originalScaleY * scale;
                  const adjustedStrokeWidth =
                    originalStrokeWidth && typeof originalStrokeWidth === "number"
                      ? originalStrokeWidth / Math.min(newScaleX, newScaleY)
                      : originalStrokeWidth;

                  obj.set({
                    left: (originalLeft - left) * scale,
                    top: (originalTop - top) * scale,
                    scaleX: newScaleX,
                    scaleY: newScaleY,
                    strokeWidth: adjustedStrokeWidth, // Adjusted to compensate for scaling
                  });

                  if ("strokeUniform" in obj) {
                    obj.strokeUniform = false;
                  }

                  if (obj.type === "textbox" || obj.type === "text") {
                    const textbox = obj as any;
                    textbox.set({
                      fontSize: 16,
                      scaleX: 1,
                      scaleY: 1,
                      lockScalingX: true,
                      lockScalingY: true,
                      hasControls: false,
                    });
                  }

                  obj.setCoords();
                }
              });

              canvas.setDimensions({ width: actualWidth, height: actualHeight });
            }
          }
        }

        setCropData(cachedState.current.cropData);
        setTransformState(cachedState.current.transformState);

        setHasPerformedCrop(cachedState.current.cropData?.isCropped || false);
      }
      setIsShowingOriginal(false);
    }
    isUndoing.current = false;
  };
};
