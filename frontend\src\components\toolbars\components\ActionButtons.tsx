import React from "react";
import { FaUndo, FaSave } from "react-icons/fa";

interface ActionButtonsProps {
  canUndo: boolean;
  isShowingOriginal: boolean;
  onUndo?: () => void;
  onSave?: () => void;
}

const ActionButtons: React.FC<ActionButtonsProps> = ({
  canUndo,
  isShowingOriginal,
  onUndo,
  onSave,
}) => {
  return (
    <>
      {onUndo && (
        <button
          className={`undo-btn ${!canUndo || isShowingOriginal ? "disabled" : ""}`}
          onClick={canUndo && !isShowingOriginal ? onUndo : undefined}
          disabled={!canUndo || isShowingOriginal}
          title="Undo"
        >
          <FaUndo />
        </button>
      )}

      <button
        className={`save-btn ${isShowingOriginal ? "disabled" : ""}`}
        onClick={isShowingOriginal ? undefined : onSave}
        disabled={isShowingOriginal}
        title="Save"
      >
        <FaSave />
      </button>
    </>
  );
};

export default ActionButtons;
