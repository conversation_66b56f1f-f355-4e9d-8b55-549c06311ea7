# CSOI Frontend

A React-based medical imaging viewer application built with TypeScript, Fabric.js, and Cornerstone.js for viewing and annotating DICOM images and volumes.

## 🏗️ Project Structure

```
frontend/
├── public/                     # Static assets
├── src/                       # Source code
│   ├── components/           # Reusable UI components
│   ├── config/              # Configuration files
│   ├── hooks/               # Custom React hooks
│   ├── layouts/             # Page layout components
│   ├── lib/                 # Core libraries and utilities
│   ├── pages/               # Page components
│   ├── routes.tsx           # Application routing
│   ├── shared/              # Shared types and utilities
│   └── styles.scss          # Global styles
├── eslint.config.js         # ESLint configuration
├── package.json             # Dependencies and scripts
├── tsconfig.json            # TypeScript configuration
└── vite.config.ts           # Vite build configuration
```

## 📁 Detailed Folder Structure

### `/src/components/`

Reusable UI components organized by functionality:

- **`toolbars/`** - Image viewer toolbars and controls
  - `FabricToolbar.tsx` - Main toolbar for Fabric.js viewer
  - `components/` - Individual toolbar components (ActionButtons, ToolGrid, etc.)
- **`viewers/`** - Medical image viewer components
  - `StackViewer.tsx` - 2D DICOM stack viewer
  - `VolumeViewer.tsx` - 3D volume viewer

### `/src/config/`

Application configuration files:

- **`defaultFabricConfigs.ts`** - Default settings for Fabric.js canvas, filters, and annotations

### `/src/hooks/`

Custom React hooks for state management and functionality:

- **`useFabricViewer.ts`** - Main hook for Fabric.js viewer functionality
- **`useImageTransforms.ts`** - Image rotation and flipping operations
- **`useFilterManagement.ts`** - Image filters (brightness, contrast, etc.)
- **`useCropManagement.ts`** - Image cropping functionality
- **`useUndoTracking.ts`** - Undo/redo operations
- **`useFabricTools.ts`** - Drawing tools management
- **`useResponsiveCanvas.ts`** - Canvas responsive resizing
- **`useVolumeResize.ts`** - Volume viewer resizing

### `/src/layouts/`

Page layout components:

- **`MainLayout.tsx`** - Main application layout wrapper

### `/src/lib/`

Core libraries and utilities organized by domain:

#### `/src/lib/fabric/`

Fabric.js integration for 2D image annotation:

- **`operations/`** - Core operations (consolidated from actions/operations)

  - `annotations.ts` - Load and manage annotations
  - `crop.ts` - Image cropping operations
  - `filters.ts` - Image filters (brightness, contrast, etc.)
  - `measurements.ts` - Distance measurement tools
  - `save.ts` - Save configurations
  - `showOriginal.ts` - Show original image functionality
  - `transforms.ts` - Rotation and flipping operations
  - `undo.ts` - Undo/redo functionality

- **`tools/`** - Drawing tools and interactions

  - `drawingInteractions.ts` - Mouse interactions for drawing shapes
  - `shapeCreators.ts` - Create initial shapes
  - `toolConfigs.ts` - Tool configuration and constraints
  - `toolDefinitions.ts` - Available drawing tools
  - `toolModeManager.ts` - Tool mode switching

- **`events/`** - Canvas event handling

  - `canvasEventListeners.ts` - Event tracking for undo/measurements

- **`canvas/`** - Canvas setup and management

  - `setup.ts` - Canvas initialization

- **`rendering/`** - Image rendering utilities
  - `image.ts` - Image loading and rendering
  - `resize.ts` - Canvas resizing utilities

#### `/src/lib/dicom/`

Cornerstone.js integration for DICOM viewing:

- **`core/`** - Core DICOM functionality
- **`config/`** - DICOM tools configuration
- **`handlers/`** - DICOM event handlers
- **`utils/`** - DICOM utilities

### `/src/pages/`

Page components:

- **`ImageViewer.tsx`** - 2D image viewer page
- **`VolumeViewer.tsx`** - 3D volume viewer page
- **`Home.tsx`** - Home page


## 🛠️ Key Technologies

- **React 18** - UI framework
- **TypeScript** - Type safety
- **Vite** - Build tool and dev server
- **Fabric.js** - 2D canvas manipulation and annotation
- **Cornerstone.js** - Medical image viewing
- **React Router** - Client-side routing
- **SCSS** - Styling

## 🎨 Styling

- **Global styles** in `styles.scss`
- **Component-specific styles** using CSS modules or inline styles
- **Responsive design** with CSS Grid and Flexbox

## 🔧 Configuration Files

- **`eslint.config.js`** - ESLint rules (any types disabled globally)
- **`tsconfig.json`** - TypeScript compiler options
- **`vite.config.ts`** - Vite build configuration with path aliases
- **`package.json`** - Dependencies and npm scripts

## 🏛️ Architecture Patterns

### Separation of Concerns

- **Components** - UI presentation
- **Hooks** - Business logic and state management
- **Lib** - Core functionality and utilities
- **Types** - Type definitions

### Consistent Naming

- **Operations** - `apply`, `create`, `handle` prefixes
- **Handlers** - `create*Handler` pattern
- **Event Listeners** - Descriptive event tracking names
- **Files** - Purpose-based naming (e.g., `drawingInteractions`, `canvasEventListeners`)




