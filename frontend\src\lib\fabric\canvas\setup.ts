import { Canvas } from "fabric";
import { CanvasConfig, FilterParams, SetupCanvasParams } from "@/shared/types";
import { loadAnnotations } from "../operations/annotations";
import { applyCanvasFilters } from "../operations/filters";
import { loadCanvasImage } from "../rendering/image";
import { createImageLoadContainer, applyCropToCanvas } from "../operations/crop";

const DEFAULT_CANVAS_CONFIG: CanvasConfig = {
  selection: true,
  backgroundColor: "transparent",
} as const;

const canvasFilterStates = new Map<Canvas, FilterParams>();

// Creates and configures an image canvas with image, filters, and annotations
export const setupImageCanvas = async ({
  canvasElement,
  imageUrl,
  annotations,
  filters,
  cropData,
  existingCanvas,
}: SetupCanvasParams): Promise<{
  canvas: Canvas;
}> => {
  // Always use the original image URL since we use viewport transforms for cropping
  const finalImageSource = imageUrl;

  // Clean up existing canvas if present
  if (existingCanvas) {
    existingCanvas.dispose();
  }

  // Create new Fabric.js canvas with default configuration
  const canvas = new Canvas(canvasElement, {
    ...DEFAULT_CANVAS_CONFIG,
  });

  // Load background image - let loadCanvasImage handle canvas dimensions
  const containerRect = canvasElement.parentElement?.getBoundingClientRect();

  if (cropData?.isCropped && cropData.normalizedCropRect) {
    const imageLoadContainer = createImageLoadContainer(cropData, containerRect);

    await loadCanvasImage(canvas, finalImageSource, {
      containerRect: imageLoadContainer || undefined,
    });

    applyCropToCanvas(canvas, cropData, containerRect);
  } else {
    await loadCanvasImage(canvas, finalImageSource, {
      containerRect: containerRect || undefined,
    });
  }

  // Apply filters if provided
  if (filters) {
    canvasFilterStates.set(canvas, { ...filters });
    canvas.renderAll();
    applyCanvasFilters(canvas, filters);
  }

  // Load annotations if provided
  if (annotations) {
    await loadAnnotations(canvas, annotations);
  }

  // Configure canvas for viewer mode (non-interactive annotations)
  canvas.selection = false;
  canvas.forEachObject((obj) => {
    const objName = (obj as unknown as Record<string, unknown>)?.name;
    if (objName !== "backgroundImage") {
      obj.selectable = false;
      obj.evented = false;
    }
  });

  return {
    canvas,
  };
};
