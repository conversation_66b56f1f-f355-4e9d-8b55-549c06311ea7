import React from "react";
import { ToolMode, ToolDefinition } from "@/shared/types";
import { toolDefinitions } from "@/lib/fabric/tools";

interface ToolGridProps {
  activeMode: ToolMode | null;
  isShowingOriginal: boolean;
  hasPerformedCrop: boolean;
  onToolSelect: (mode: ToolMode) => void;
  onCrop?: () => void;
}

const ToolGrid: React.FC<ToolGridProps> = ({
  activeMode,
  isShowingOriginal,
  hasPerformedCrop,
  onToolSelect,
  onCrop,
}) => {
  const handleToolClick = async (tool: ToolDefinition) => {
    if (isShowingOriginal) return;

    if (tool.mode === "crop" && hasPerformedCrop) {
      if (onCrop) {
        await onCrop();
        onToolSelect("crop");
      }
      return;
    }

    onToolSelect(tool.mode);
  };

  return (
    <div className="tool-grid">
      {toolDefinitions.map((tool) => {
        const IconComponent = tool.icon;
        const isDisabled = isShowingOriginal;
        const isCropInactive = tool.mode === "crop" && hasPerformedCrop;

        return (
          <button
            key={tool.mode}
            className={`tool-btn ${activeMode === tool.mode ? "active" : ""} ${
              isDisabled ? "disabled" : ""
            } ${isCropInactive ? "inactive" : ""}`}
            onClick={() => handleToolClick(tool)}
            disabled={isDisabled}
            title={
              isShowingOriginal
                ? "Disabled in show original mode"
                : tool.mode === "crop" && hasPerformedCrop
                ? "Restore original image"
                : tool.title
            }
          >
            <IconComponent />
          </button>
        );
      })}
    </div>
  );
};

export default ToolGrid;
